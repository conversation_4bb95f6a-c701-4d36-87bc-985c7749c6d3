<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>_04 OS</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pArmCC>5060528::V5.06 update 5 (build 528)::ARMCC</pArmCC>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32F407ZG</Device>
          <Vendor>STMicroelectronics</Vendor>
          <PackID>Keil.STM32F4xx_DFP.1.0.8</PackID>
          <PackURL>http://www.keil.com/pack</PackURL>
          <Cpu>IROM(0x08000000,0x100000) IRAM(0x20000000,0x20000) IRAM2(0x10000000,0x10000) CPUTYPE("Cortex-M4") FPU2 CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0STM32F4xx_1024 -********** -********* -FP0($$Device:STM32F407ZG$Flash\STM32F4xx_1024.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:STM32F407ZG$Device\Include\stm32f4xx.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:STM32F407ZG$SVD\STM32F40x.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Output\</OutputDirectory>
          <OutputName>_04</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Output\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM4</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM4</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
          <Simulator>
            <UseSimulator>0</UseSimulator>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>1</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <LimitSpeedToRealTime>0</LimitSpeedToRealTime>
            <RestoreSysVw>1</RestoreSysVw>
          </Simulator>
          <Target>
            <UseTarget>1</UseTarget>
            <LoadApplicationAtStartup>1</LoadApplicationAtStartup>
            <RunToMain>1</RunToMain>
            <RestoreBreakpoints>1</RestoreBreakpoints>
            <RestoreWatchpoints>1</RestoreWatchpoints>
            <RestoreMemoryDisplay>1</RestoreMemoryDisplay>
            <RestoreFunctions>0</RestoreFunctions>
            <RestoreToolbox>1</RestoreToolbox>
            <RestoreTracepoints>1</RestoreTracepoints>
            <RestoreSysVw>1</RestoreSysVw>
          </Target>
          <RunDebugAfterBuild>0</RunDebugAfterBuild>
          <TargetSelection>3</TargetSelection>
          <SimDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
          </SimDlls>
          <TargetDlls>
            <CpuDll></CpuDll>
            <CpuDllArguments></CpuDllArguments>
            <PeripheralDll></PeripheralDll>
            <PeripheralDllArguments></PeripheralDllArguments>
            <InitializationFile></InitializationFile>
            <Driver>Segger\JL2CM3.dll</Driver>
          </TargetDlls>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M4"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>2</RvdsVP>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>0</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x10000000</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>0</uC99>
            <useXO>0</useXO>
            <v6Lang>1</v6Lang>
            <v6LangP>1</v6LangP>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>STM32F40_41xxx,USE_STDPERIPH_DRIVER,ARM_MATH_CM4,__FPU_PRESENT = 1,__FPU_USED=1</Define>
              <Undefine></Undefine>
              <IncludePath>.\_01_App;.\_02_Core;.\_04_FWLib\STM32F40x_FWLib\inc;.\_05_Os;.\_06_System;.\_07_TFT_LCD;.\_03_Drive;..\02_OS;.\_08_USB\STM32_USB_Device_Library\Core\inc;.\_08_USB\STM32_USB_HOST_Library\Core\inc;.\_08_USB\STM32_USB_OTG_Driver\inc;.\_08_USB\USB_APP;.\_08_USB\STM32_USB_HOST_Library\Class\HID\inc</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>App</GroupName>
          <GroupOption>
            <CommonProperty>
              <UseCPPCompiler>0</UseCPPCompiler>
              <RVCTCodeConst>0</RVCTCodeConst>
              <RVCTZI>0</RVCTZI>
              <RVCTOtherData>0</RVCTOtherData>
              <ModuleSelection>0</ModuleSelection>
              <IncludeInBuild>2</IncludeInBuild>
              <AlwaysBuild>2</AlwaysBuild>
              <GenerateAssemblyFile>2</GenerateAssemblyFile>
              <AssembleAssemblyFile>2</AssembleAssemblyFile>
              <PublicsOnly>2</PublicsOnly>
              <StopOnExitCode>11</StopOnExitCode>
              <CustomArgument></CustomArgument>
              <IncludeLibraryModules></IncludeLibraryModules>
              <ComprImg>1</ComprImg>
            </CommonProperty>
            <GroupArmAds>
              <Cads>
                <interw>2</interw>
                <Optim>0</Optim>
                <oTime>2</oTime>
                <SplitLS>2</SplitLS>
                <OneElfS>2</OneElfS>
                <Strict>2</Strict>
                <EnumInt>2</EnumInt>
                <PlainCh>2</PlainCh>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <wLevel>2</wLevel>
                <uThumb>2</uThumb>
                <uSurpInc>2</uSurpInc>
                <uC99>2</uC99>
                <useXO>2</useXO>
                <v6Lang>0</v6Lang>
                <v6LangP>0</v6LangP>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath>.\_01_App</IncludePath>
                </VariousControls>
              </Cads>
              <Aads>
                <interw>2</interw>
                <Ropi>2</Ropi>
                <Rwpi>2</Rwpi>
                <thumb>2</thumb>
                <SplitLS>2</SplitLS>
                <SwStkChk>2</SwStkChk>
                <NoWarn>2</NoWarn>
                <uSurpInc>2</uSurpInc>
                <useXO>2</useXO>
                <VariousControls>
                  <MiscControls></MiscControls>
                  <Define></Define>
                  <Undefine></Undefine>
                  <IncludePath></IncludePath>
                </VariousControls>
              </Aads>
            </GroupArmAds>
          </GroupOption>
          <Files>
            <File>
              <FileName>App_Touch.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_01_App\App_Touch.c</FilePath>
            </File>
            <File>
              <FileName>App_LED.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_01_App\App_LED.c</FilePath>
            </File>
            <File>
              <FileName>User.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_01_App\User.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>User</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\main.c</FilePath>
            </File>
            <File>
              <FileName>User_header.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\_05_Os\User_header.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>OS</GroupName>
          <Files>
            <File>
              <FileName>Os_cpu.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_05_Os\Os_cpu.c</FilePath>
            </File>
            <File>
              <FileName>Os_UI.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_05_Os\Os_UI.c</FilePath>
            </File>
            <File>
              <FileName>Os_malloc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_05_Os\Os_malloc.c</FilePath>
            </File>
            <File>
              <FileName>core.asm</FileName>
              <FileType>2</FileType>
              <FilePath>.\_05_Os\core.asm</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>SYSTEM</GroupName>
          <Files>
            <File>
              <FileName>sys.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_06_System\sys.c</FilePath>
            </File>
            <File>
              <FileName>delay.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_06_System\delay.c</FilePath>
            </File>
            <File>
              <FileName>usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_06_System\usart.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Drive</GroupName>
          <Files>
            <File>
              <FileName>Drive_GPIO.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\Drive_GPIO.c</FilePath>
            </File>
            <File>
              <FileName>Drive_DMA.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\Drive_DMA.c</FilePath>
            </File>
            <File>
              <FileName>Drive_Timer.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\Drive_Timer.c</FilePath>
            </File>
            <File>
              <FileName>Drive_Touch.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\Drive_Touch.c</FilePath>
            </File>
            <File>
              <FileName>Drive_TouchKey.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\Drive_TouchKey.c</FilePath>
            </File>
            <File>
              <FileName>User_ADC.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\User_ADC.c</FilePath>
            </File>
            <File>
              <FileName>Drive_PWM.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\Drive_PWM.c</FilePath>
            </File>
            <File>
              <FileName>User_SPI.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\User_SPI.c</FilePath>
            </File>
            <File>
              <FileName>User_BGD.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\User_BGD.c</FilePath>
            </File>
            <File>
              <FileName>Drive_PS2.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\Drive_PS2.c</FilePath>
            </File>
            <File>
              <FileName>User_DAC8562.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\User_DAC8562.c</FilePath>
            </File>
            <File>
              <FileName>User_AD8370.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\User_AD8370.c</FilePath>
            </File>
            <File>
              <FileName>User_IIC.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\User_IIC.c</FilePath>
            </File>
            <File>
              <FileName>Drive_Communication.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\Drive_Communication.c</FilePath>
            </File>
            <File>
              <FileName>Drive_ADS1256.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\Drive_ADS1256.c</FilePath>
            </File>
            <File>
              <FileName>Drive_FFT.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\Drive_FFT.c</FilePath>
            </File>
            <File>
              <FileName>User_PGA2310.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\User_PGA2310.c</FilePath>
            </File>
            <File>
              <FileName>User_DAC.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\User_DAC.c</FilePath>
            </File>
            <File>
              <FileName>Drive_DAC.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\Drive_DAC.c</FilePath>
            </File>
            <File>
              <FileName>Drive_PGA4311.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\Drive_PGA4311.c</FilePath>
            </File>
            <File>
              <FileName>Drive_PGA4311.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\_03_Drive\Drive_PGA4311.h</FilePath>
            </File>
            <File>
              <FileName>Drive_SI5351A.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\Drive_SI5351A.c</FilePath>
            </File>
            <File>
              <FileName>Drive_SI5351A.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\_03_Drive\Drive_SI5351A.h</FilePath>
            </File>
            <File>
              <FileName>Drive_ADF4351.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_03_Drive\Drive_ADF4351.c</FilePath>
            </File>
            <File>
              <FileName>Drive_ADF4351.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\_03_Drive\Drive_ADF4351.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>TFT</GroupName>
          <Files>
            <File>
              <FileName>Character.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_07_TFT_LCD\Character.c</FilePath>
            </File>
            <File>
              <FileName>fonts.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_07_TFT_LCD\fonts.c</FilePath>
            </File>
            <File>
              <FileName>fontupd.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_07_TFT_LCD\fontupd.c</FilePath>
            </File>
            <File>
              <FileName>spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_07_TFT_LCD\spi.c</FilePath>
            </File>
            <File>
              <FileName>text.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_07_TFT_LCD\text.c</FilePath>
            </File>
            <File>
              <FileName>TFT_LCD.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_07_TFT_LCD\TFT_LCD.c</FilePath>
            </File>
            <File>
              <FileName>W25Q64.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_07_TFT_LCD\W25Q64.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FWLIB</GroupName>
          <Files>
            <File>
              <FileName>stm32f4xx_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_04_FWLib\STM32F40x_FWLib\src\stm32f4xx_adc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_04_FWLib\STM32F40x_FWLib\src\stm32f4xx_crc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_04_FWLib\STM32F40x_FWLib\src\stm32f4xx_dac.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_04_FWLib\STM32F40x_FWLib\src\stm32f4xx_dma.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_04_FWLib\STM32F40x_FWLib\src\stm32f4xx_flash.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_04_FWLib\STM32F40x_FWLib\src\stm32f4xx_exti.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_fsmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_04_FWLib\STM32F40x_FWLib\src\stm32f4xx_fsmc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_04_FWLib\STM32F40x_FWLib\src\stm32f4xx_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_04_FWLib\STM32F40x_FWLib\src\stm32f4xx_i2c.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_04_FWLib\STM32F40x_FWLib\src\stm32f4xx_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_04_FWLib\STM32F40x_FWLib\src\stm32f4xx_tim.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_04_FWLib\STM32F40x_FWLib\src\stm32f4xx_usart.c</FilePath>
            </File>
            <File>
              <FileName>misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_04_FWLib\STM32F40x_FWLib\src\misc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_04_FWLib\STM32F40x_FWLib\src\stm32f4xx_spi.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_syscfg.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_04_FWLib\STM32F40x_FWLib\src\stm32f4xx_syscfg.c</FilePath>
            </File>
            <File>
              <FileName>stm32f4xx_can.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_04_FWLib\STM32F40x_FWLib\src\stm32f4xx_can.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Core</GroupName>
          <Files>
            <File>
              <FileName>stm32f4xx_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_02_Core\stm32f4xx_it.c</FilePath>
            </File>
            <File>
              <FileName>system_stm32f4xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>.\_02_Core\system_stm32f4xx.c</FilePath>
            </File>
            <File>
              <FileName>arm_math.h</FileName>
              <FileType>5</FileType>
              <FilePath>.\_02_Core\arm_math.h</FilePath>
            </File>
            <File>
              <FileName>startup_stm32f40_41xxx.s</FileName>
              <FileType>2</FileType>
              <FilePath>.\_02_Core\startup_stm32f40_41xxx.s</FilePath>
            </File>
            <File>
              <FileName>arm_cortexM4lf_math.lib</FileName>
              <FileType>4</FileType>
              <FilePath>.\_02_Core\arm_cortexM4lf_math.lib</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

</Project>
