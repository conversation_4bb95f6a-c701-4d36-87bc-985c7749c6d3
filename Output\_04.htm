<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Output\_04.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Output\_04.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060528: Last Updated: Sun Jul 28 13:48:19 2024
<BR><P>
<H3>Maximum Stack Usage =        336 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
User_main &rArr; MenuHaddler_5 &rArr; Change_Menu &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[7a]">__user_initial_stackheap</a>
 <LI><a href="#[a4]">OS_CPU_SR_Save</a>
 <LI><a href="#[a6]">OS_CPU_SR_Restore</a>
 <LI><a href="#[b4]">OSCtxSw</a>
 <LI><a href="#[b9]">OSStartHighRdy</a>
 <LI><a href="#[c]">PendSV_Handler</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[20]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[20]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[20]">ADC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[8]">BusFault_Handler</a> from stm32f4xx_it.o(i.BusFault_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[22]">CAN1_RX0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[23]">CAN1_RX1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[24]">CAN1_SCE_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[21]">CAN1_TX_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4e]">CAN2_RX0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4f]">CAN2_RX1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[50]">CAN2_SCE_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4d]">CAN2_TX_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5d]">CRYP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5c]">DCMI_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[19]">DMA1_Stream0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1a]">DMA1_Stream1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1b]">DMA1_Stream2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1c]">DMA1_Stream3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1d]">DMA1_Stream4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1e]">DMA1_Stream5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1f]">DMA1_Stream6_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3d]">DMA1_Stream7_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[46]">DMA2_Stream0_IRQHandler</a> from user_adc.o(i.DMA2_Stream0_IRQHandler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[47]">DMA2_Stream1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[48]">DMA2_Stream2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[49]">DMA2_Stream3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4a]">DMA2_Stream4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[52]">DMA2_Stream5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[53]">DMA2_Stream6_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[54]">DMA2_Stream7_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[b]">DebugMon_Handler</a> from stm32f4xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4b]">ETH_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4c]">ETH_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[14]">EXTI0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[36]">EXTI15_10_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[15]">EXTI1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[16]">EXTI2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[17]">EXTI3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[18]">EXTI4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[25]">EXTI9_5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[12]">FLASH_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5f]">FPU_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3e]">FSMC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5e]">HASH_RNG_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[6]">HardFault_Handler</a> from stm32f4xx_it.o(i.HardFault_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2e]">I2C1_ER_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2d]">I2C1_EV_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[30]">I2C2_ER_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2f]">I2C2_EV_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[57]">I2C3_ER_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[56]">I2C3_EV_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[66]">LED_main</a> from app_led.o(i.LED_main) referenced from main.o(i.main)
 <LI><a href="#[7]">MemManage_Handler</a> from stm32f4xx_it.o(i.MemManage_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[65]">MyPs2KeyScan</a> from drive_ps2.o(i.MyPs2KeyScan) referenced from main.o(i.main)
 <LI><a href="#[5]">NMI_Handler</a> from stm32f4xx_it.o(i.NMI_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[62]">OS_IDLE_Task</a> from os_cpu.o(i.OS_IDLE_Task) referenced from os_cpu.o(i.OS_Start)
 <LI><a href="#[51]">OTG_FS_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[38]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[59]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[58]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5b]">OTG_HS_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5a]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[f]">PVD_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[c]">PendSV_Handler</a> from core.o(CODE) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[13]">RCC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[37]">RTC_Alarm_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[11]">RTC_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4]">Reset_Handler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3f]">SDIO_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[31]">SPI1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[32]">SPI2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[41]">SPI3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[a]">SVC_Handler</a> from stm32f4xx_it.o(i.SVC_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[d]">SysTick_Handler</a> from os_cpu.o(i.SysTick_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[60]">SystemInit</a> from system_stm32f4xx.o(i.SystemInit) referenced from startup_stm32f40_41xxx.o(.text)
 <LI><a href="#[10]">TAMP_STAMP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[26]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[29]">TIM1_CC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[28]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[27]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2a]">TIM2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2b]">TIM3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2c]">TIM4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[40]">TIM5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[44]">TIM6_DAC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[45]">TIM7_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[39]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3c]">TIM8_CC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3b]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3a]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[63]">Task_End</a> from os_cpu.o(i.Task_End) referenced from os_cpu.o(i.Task_Create)
 <LI><a href="#[42]">UART4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[43]">UART5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[33]">USART1_IRQHandler</a> from usart.o(i.USART1_IRQHandler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[34]">USART2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[35]">USART3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[55]">USART6_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[9]">UsageFault_Handler</a> from stm32f4xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[64]">User_main</a> from user.o(i.User_main) referenced from main.o(i.main)
 <LI><a href="#[e]">WWDG_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[67]">__main</a> from __main.o(!!!main) referenced from startup_stm32f40_41xxx.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[67]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[68]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[6a]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[d9]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[da]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[6b]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[db]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[71]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[6c]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[dc]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[dd]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[de]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[df]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[e0]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[e1]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[e2]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[e3]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[e4]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[e5]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[e6]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[e7]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[e8]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[e9]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[ea]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[eb]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[ec]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[ed]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[ee]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[ef]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[76]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[f0]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[f1]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[f2]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[f3]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[f4]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[f5]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[f6]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[69]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[f7]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[6e]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[70]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[f8]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[72]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 336 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; OS_Init &rArr; OS_LCD_Init &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[f9]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[7b]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[75]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[fa]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[77]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[4]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>CRYP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>FSMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[7a]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32f40_41xxx.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[fb]"></a>__use_no_semihosting</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi_2.o(.text), UNUSED)

<P><STRONG><a name="[80]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDSDataInit
</UL>

<P><STRONG><a name="[fc]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[fd]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[fe]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[ff]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[100]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[101]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[102]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[103]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[104]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[79]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[105]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[6f]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[74]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[a4]"></a>OS_CPU_SR_Save</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, core.o(CODE))
<BR><BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_SchedUnlock
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_SchedLock
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Sched
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSTimeDly
</UL>

<P><STRONG><a name="[a6]"></a>OS_CPU_SR_Restore</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, core.o(CODE))
<BR><BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_SchedUnlock
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_SchedLock
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Sched
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSTimeDly
</UL>

<P><STRONG><a name="[b4]"></a>OSCtxSw</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, core.o(CODE))
<BR><BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Sched
</UL>

<P><STRONG><a name="[b9]"></a>OSStartHighRdy</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, core.o(CODE))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Start
</UL>

<P><STRONG><a name="[c]"></a>PendSV_Handler</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, core.o(CODE))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[7c]"></a>Change_Menu</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, user.o(i.Change_Menu))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = Change_Menu &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_String_Show
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Appoint_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_5
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_4
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_3
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_2
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_1
</UL>

<P><STRONG><a name="[7f]"></a>DDSDataInit</STRONG> (Thumb, 158 bytes, Stack size 224 bytes, drive_communication.o(i.DDSDataInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 280 + Unknown Stack Size
<LI>Call Chain = DDSDataInit &rArr; delay_ms &rArr; delay_us &rArr; OS_SchedUnlock
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendData
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_All
</UL>

<P><STRONG><a name="[46]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, user_adc.o(i.DMA2_Stream0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA2_Stream0_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_ClearFlag
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[83]"></a>DMA_ClearFlag</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, stm32f4xx_dma.o(i.DMA_ClearFlag))
<BR><BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream0_IRQHandler
</UL>

<P><STRONG><a name="[b]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[85]"></a>Disp_Main</STRONG> (Thumb, 280 bytes, Stack size 16 bytes, user.o(i.Disp_Main))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = Disp_Main &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_String_Show
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Appoint_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
</UL>

<P><STRONG><a name="[98]"></a>FSMC_NORSRAMCmd</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, stm32f4xx_fsmc.o(i.FSMC_NORSRAMCmd))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_FSMCConfig
</UL>

<P><STRONG><a name="[97]"></a>FSMC_NORSRAMInit</STRONG> (Thumb, 230 bytes, Stack size 0 bytes, stm32f4xx_fsmc.o(i.FSMC_NORSRAMInit))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_FSMCConfig
</UL>

<P><STRONG><a name="[88]"></a>GPIO_Init</STRONG> (Thumb, 144 bytes, Stack size 20 bytes, stm32f4xx_gpio.o(i.GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA4311_Init
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Uart
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_GPIO_Config
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_SCL_Set
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_GPIO_Init
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_CtrlLinesConfig
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Init
</UL>

<P><STRONG><a name="[8d]"></a>GPIO_PinAFConfig</STRONG> (Thumb, 70 bytes, Stack size 20 bytes, stm32f4xx_gpio.o(i.GPIO_PinAFConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = GPIO_PinAFConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Uart
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_CtrlLinesConfig
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[c3]"></a>GPIO_ReadInputDataBit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_ReadKeyCodon
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_SCL_Wait
</UL>

<P><STRONG><a name="[bc]"></a>GPIO_ResetBits</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(i.GPIO_ResetBits))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Read
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA4311_Init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_SCL_Set
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA4311_SetGain
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_ReadID
</UL>

<P><STRONG><a name="[bb]"></a>GPIO_SetBits</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(i.GPIO_SetBits))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Read
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA4311_Init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_SCL_Set
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA4311_SetGain
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_ReadID
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Init
</UL>

<P><STRONG><a name="[6]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[86]"></a>I2C_GPIO_Config</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, drive_si5351a.o(i.I2C_GPIO_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = I2C_GPIO_Config &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_All
</UL>

<P><STRONG><a name="[89]"></a>Init_All</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, user.o(i.Init_All))
<BR><BR>[Stack]<UL><LI>Max Depth = 288 + Unknown Stack Size
<LI>Call Chain = Init_All &rArr; DDSDataInit &rArr; delay_ms &rArr; delay_us &rArr; OS_SchedUnlock
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Uart
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_GPIO_Config
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDSDataInit
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
</UL>

<P><STRONG><a name="[8b]"></a>Init_Uart</STRONG> (Thumb, 124 bytes, Stack size 32 bytes, drive_communication.o(i.Init_Uart))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = Init_Uart &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_All
</UL>

<P><STRONG><a name="[90]"></a>Key_StateSweep</STRONG> (Thumb, 192 bytes, Stack size 8 bytes, drive_ps2.o(i.Key_StateSweep))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = Key_StateSweep &rArr; PS2_ReadKeyCodon &rArr; PS2_SCL_Wait &rArr; PS2_SCL_Set &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_ReadKeyCodon
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MyPs2KeyScan
</UL>

<P><STRONG><a name="[7d]"></a>LCD_Appoint_Clear</STRONG> (Thumb, 72 bytes, Stack size 32 bytes, tft_lcd.o(i.LCD_Appoint_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = LCD_Appoint_Clear &rArr; LCD_SetCursor
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM_Prepare
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Disp_Main
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Change_Menu
</UL>

<P><STRONG><a name="[8a]"></a>LCD_Clear</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, tft_lcd.o(i.LCD_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = LCD_Clear &rArr; LCD_SetCursor
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM_Prepare
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_LCD_Init
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_All
</UL>

<P><STRONG><a name="[94]"></a>LCD_CtrlLinesConfig</STRONG> (Thumb, 322 bytes, Stack size 16 bytes, tft_lcd.o(i.LCD_CtrlLinesConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = LCD_CtrlLinesConfig &rArr; GPIO_PinAFConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TFT_LCD_Init
</UL>

<P><STRONG><a name="[95]"></a>LCD_FSMCConfig</STRONG> (Thumb, 94 bytes, Stack size 96 bytes, tft_lcd.o(i.LCD_FSMCConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = LCD_FSMCConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB3PeriphClockCmd
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSMC_NORSRAMInit
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSMC_NORSRAMCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TFT_LCD_Init
</UL>

<P><STRONG><a name="[99]"></a>LCD_REG_Select</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, tft_lcd.o(i.LCD_REG_Select))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TFT_LCD_Init
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM_Prepare
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetDisplayWindow
</UL>

<P><STRONG><a name="[92]"></a>LCD_SetCursor</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, tft_lcd.o(i.LCD_SetCursor))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LCD_SetCursor
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_REG_Select
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Point_Draw
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Appoint_Clear
</UL>

<P><STRONG><a name="[9b]"></a>LCD_SetDisplayWindow</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, tft_lcd.o(i.LCD_SetDisplayWindow))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = LCD_SetDisplayWindow
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_REG_Select
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TFT_LCD_Init
</UL>

<P><STRONG><a name="[9a]"></a>LCD_WriteRAM</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, tft_lcd.o(i.LCD_WriteRAM))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TFT_LCD_Init
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Point_Draw
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetDisplayWindow
</UL>

<P><STRONG><a name="[93]"></a>LCD_WriteRAM_Prepare</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, tft_lcd.o(i.LCD_WriteRAM_Prepare))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LCD_WriteRAM_Prepare
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_REG_Select
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Point_Draw
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Appoint_Clear
</UL>

<P><STRONG><a name="[9d]"></a>LED_Control</STRONG> (Thumb, 152 bytes, Stack size 0 bytes, app_led.o(i.LED_Control))
<BR><BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_main
</UL>

<P><STRONG><a name="[9c]"></a>LED_Init</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, drive_gpio.o(i.LED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = LED_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Init
</UL>

<P><STRONG><a name="[66]"></a>LED_main</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, app_led.o(i.LED_main))
<BR><BR>[Stack]<UL><LI>Max Depth = 24 + Unknown Stack Size
<LI>Call Chain = LED_main &rArr; OSTimeDly &rArr; OS_Sched
</UL>
<BR>[Calls]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Control
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSTimeDly
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[7]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[9f]"></a>MenuHaddler_1</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, user.o(i.MenuHaddler_1))
<BR><BR>[Stack]<UL><LI>Max Depth = 336 + Unknown Stack Size
<LI>Call Chain = MenuHaddler_1 &rArr; Change_Menu &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Change_Menu
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
</UL>

<P><STRONG><a name="[a0]"></a>MenuHaddler_2</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, user.o(i.MenuHaddler_2))
<BR><BR>[Stack]<UL><LI>Max Depth = 336 + Unknown Stack Size
<LI>Call Chain = MenuHaddler_2 &rArr; Change_Menu &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Change_Menu
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
</UL>

<P><STRONG><a name="[a1]"></a>MenuHaddler_3</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, user.o(i.MenuHaddler_3))
<BR><BR>[Stack]<UL><LI>Max Depth = 336 + Unknown Stack Size
<LI>Call Chain = MenuHaddler_3 &rArr; Change_Menu &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Change_Menu
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
</UL>

<P><STRONG><a name="[a2]"></a>MenuHaddler_4</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, user.o(i.MenuHaddler_4))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = MenuHaddler_4 &rArr; Change_Menu &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Change_Menu
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
</UL>

<P><STRONG><a name="[a3]"></a>MenuHaddler_5</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, user.o(i.MenuHaddler_5))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = MenuHaddler_5 &rArr; Change_Menu &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Change_Menu
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
</UL>

<P><STRONG><a name="[65]"></a>MyPs2KeyScan</STRONG> (Thumb, 456 bytes, Stack size 0 bytes, drive_ps2.o(i.MyPs2KeyScan))
<BR><BR>[Stack]<UL><LI>Max Depth = 68 + Unknown Stack Size
<LI>Call Chain = MyPs2KeyScan &rArr; Key_StateSweep &rArr; PS2_ReadKeyCodon &rArr; PS2_SCL_Wait &rArr; PS2_SCL_Set &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_StateSweep
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[5]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[a5]"></a>OSDelPrioRdy</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, os_cpu.o(i.OSDelPrioRdy))
<BR><BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSTimeDly
</UL>

<P><STRONG><a name="[b3]"></a>OSGetHighRdy</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, os_cpu.o(i.OSGetHighRdy))
<BR><BR>[Called By]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Sched
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Start
</UL>

<P><STRONG><a name="[cd]"></a>OSSetPrioRdy</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, os_cpu.o(i.OSSetPrioRdy))
<BR><BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Task_Create
</UL>

<P><STRONG><a name="[9e]"></a>OSTimeDly</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, os_cpu.o(i.OSTimeDly))
<BR><BR>[Stack]<UL><LI>Max Depth = 24 + Unknown Stack Size
<LI>Call Chain = OSTimeDly &rArr; OS_Sched
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Save
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Restore
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Sched
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSDelPrioRdy
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_main
</UL>

<P><STRONG><a name="[a8]"></a>OS_Char_Show</STRONG> (Thumb, 266 bytes, Stack size 40 bytes, os_ui.o(i.OS_Char_Show))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = OS_Char_Show &rArr; OS_Point_Draw &rArr; LCD_SetCursor
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Point_Draw
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_String_Show
</UL>

<P><STRONG><a name="[aa]"></a>OS_Font_Show</STRONG> (Thumb, 192 bytes, Stack size 200 bytes, os_ui.o(i.OS_Font_Show))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Point_Draw
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_HzMat_Get
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_String_Show
</UL>

<P><STRONG><a name="[ab]"></a>OS_HzMat_Get</STRONG> (Thumb, 218 bytes, Stack size 40 bytes, os_ui.o(i.OS_HzMat_Get))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Read
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Font_Show
</UL>

<P><STRONG><a name="[62]"></a>OS_IDLE_Task</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, os_cpu.o(i.OS_IDLE_Task))
<BR>[Address Reference Count : 1]<UL><LI> os_cpu.o(i.OS_Start)
</UL>
<P><STRONG><a name="[ad]"></a>OS_Init</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, main.o(i.OS_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 328 + Unknown Stack Size
<LI>Call Chain = OS_Init &rArr; OS_LCD_Init &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_Keyboard_Init
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_LCD_Init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[b0]"></a>OS_LCD_Init</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, os_ui.o(i.OS_LCD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 320 + Unknown Stack Size
<LI>Call Chain = OS_LCD_Init &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;font_init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TFT_LCD_Init
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_String_Show
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Init
</UL>

<P><STRONG><a name="[a9]"></a>OS_Point_Draw</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, os_ui.o(i.OS_Point_Draw))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = OS_Point_Draw &rArr; LCD_SetCursor
</UL>
<BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM_Prepare
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetCursor
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Font_Show
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Char_Show
</UL>

<P><STRONG><a name="[a7]"></a>OS_Sched</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, os_cpu.o(i.OS_Sched))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = OS_Sched
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Save
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Restore
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSCtxSw
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSGetHighRdy
</UL>
<BR>[Called By]<UL><LI><a href="#[d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSTimeDly
</UL>

<P><STRONG><a name="[b5]"></a>OS_SchedLock</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, os_cpu.o(i.OS_SchedLock))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = OS_SchedLock
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Save
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Restore
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>

<P><STRONG><a name="[b6]"></a>OS_SchedUnlock</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, os_cpu.o(i.OS_SchedUnlock))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = OS_SchedUnlock
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Save
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Restore
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>

<P><STRONG><a name="[b7]"></a>OS_Start</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, os_cpu.o(i.OS_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 32 + Unknown Stack Size
<LI>Call Chain = OS_Start &rArr; Task_Create
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSStartHighRdy
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSGetHighRdy
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Task_Create
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7e]"></a>OS_String_Show</STRONG> (Thumb, 124 bytes, Stack size 40 bytes, os_ui.o(i.OS_String_Show))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Font_Show
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Char_Show
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_LCD_Init
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Disp_Main
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Change_Menu
</UL>

<P><STRONG><a name="[ba]"></a>PGA4311_Init</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, drive_pga4311.o(i.PGA4311_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80 + Unknown Stack Size
<LI>Call Chain = PGA4311_Init &rArr; PGA4311_Set &rArr; PGA4311_SetGain &rArr; delay_us &rArr; OS_SchedUnlock
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA4311_Set
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
</UL>

<P><STRONG><a name="[bd]"></a>PGA4311_Set</STRONG> (Thumb, 132 bytes, Stack size 8 bytes, drive_pga4311.o(i.PGA4311_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 64 + Unknown Stack Size
<LI>Call Chain = PGA4311_Set &rArr; PGA4311_SetGain &rArr; delay_us &rArr; OS_SchedUnlock
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA4311_SetGain
</UL>
<BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA4311_Init
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
</UL>

<P><STRONG><a name="[be]"></a>PGA4311_SetGain</STRONG> (Thumb, 142 bytes, Stack size 16 bytes, drive_pga4311.o(i.PGA4311_SetGain))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = PGA4311_SetGain &rArr; delay_us &rArr; OS_SchedUnlock
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA4311_Set
</UL>

<P><STRONG><a name="[af]"></a>PS2_Keyboard_Init</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, drive_ps2.o(i.PS2_Keyboard_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = PS2_Keyboard_Init &rArr; PS2_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Init
</UL>

<P><STRONG><a name="[91]"></a>PS2_ReadKeyCodon</STRONG> (Thumb, 170 bytes, Stack size 16 bytes, drive_ps2.o(i.PS2_ReadKeyCodon))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = PS2_ReadKeyCodon &rArr; PS2_SCL_Wait &rArr; PS2_SCL_Set &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadInputDataBit
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_SCL_Wait
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_SCL_Set
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_StateSweep
</UL>

<P><STRONG><a name="[87]"></a>RCC_AHB1PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA4311_Init
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Uart
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_GPIO_Config
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_SCL_Set
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_GPIO_Init
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_CtrlLinesConfig
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Init
</UL>

<P><STRONG><a name="[96]"></a>RCC_AHB3PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_FSMCConfig
</UL>

<P><STRONG><a name="[8c]"></a>RCC_APB2PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Uart
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[c5]"></a>RCC_APB2PeriphResetCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[d2]"></a>RCC_GetClocksFreq</STRONG> (Thumb, 214 bytes, Stack size 20 bytes, stm32f4xx_rcc.o(i.RCC_GetClocksFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
</UL>

<P><STRONG><a name="[c4]"></a>SPI1_Init</STRONG> (Thumb, 198 bytes, Stack size 32 bytes, spi.o(i.SPI1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = SPI1_Init &rArr; GPIO_PinAFConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Init
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphResetCmd
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Cmd
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Init
</UL>

<P><STRONG><a name="[c8]"></a>SPI1_ReadWriteByte</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, spi.o(i.SPI1_ReadWriteByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_I2S_SendData
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_I2S_ReceiveData
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_I2S_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Read
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_ReadID
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[cc]"></a>SPI1_SetSpeed</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, spi.o(i.SPI1_SetSpeed))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI1_SetSpeed
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Init
</UL>

<P><STRONG><a name="[c7]"></a>SPI_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_spi.o(i.SPI_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_SetSpeed
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[c9]"></a>SPI_I2S_GetFlagStatus</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>

<P><STRONG><a name="[cb]"></a>SPI_I2S_ReceiveData</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_spi.o(i.SPI_I2S_ReceiveData))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>

<P><STRONG><a name="[ca]"></a>SPI_I2S_SendData</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_spi.o(i.SPI_I2S_SendData))
<BR><BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>

<P><STRONG><a name="[c6]"></a>SPI_Init</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, stm32f4xx_spi.o(i.SPI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SPI_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>

<P><STRONG><a name="[a]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[cf]"></a>SysTick_CLKSourceConfig</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, misc.o(i.SysTick_CLKSourceConfig))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_init
</UL>

<P><STRONG><a name="[d]"></a>SysTick_Handler</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, os_cpu.o(i.SysTick_Handler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24 + Unknown Stack Size
<LI>Call Chain = SysTick_Handler &rArr; OS_Sched
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Save
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_CPU_SR_Restore
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Sched
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSSetPrioRdy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>SystemInit</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, system_stm32f4xx.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SystemInit &rArr; SetSysClock
</UL>
<BR>[Calls]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(.text)
</UL>
<P><STRONG><a name="[ae]"></a>System_init</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, os_cpu.o(i.System_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = System_init
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_CLKSourceConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Init
</UL>

<P><STRONG><a name="[b1]"></a>TFT_LCD_Init</STRONG> (Thumb, 496 bytes, Stack size 8 bytes, tft_lcd.o(i.TFT_LCD_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104 + Unknown Stack Size
<LI>Call Chain = TFT_LCD_Init &rArr; LCD_FSMCConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_WriteRAM
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SetDisplayWindow
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_REG_Select
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_FSMCConfig
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_CtrlLinesConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_LCD_Init
</UL>

<P><STRONG><a name="[84]"></a>TIM_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_tim.o(i.TIM_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream0_IRQHandler
</UL>

<P><STRONG><a name="[b8]"></a>Task_Create</STRONG> (Thumb, 212 bytes, Stack size 24 bytes, os_cpu.o(i.Task_Create))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Task_Create
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSSetPrioRdy
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Start
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[63]"></a>Task_End</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, os_cpu.o(i.Task_End))
<BR>[Address Reference Count : 1]<UL><LI> os_cpu.o(i.Task_Create)
</UL>
<P><STRONG><a name="[33]"></a>USART1_IRQHandler</STRONG> (Thumb, 122 bytes, Stack size 8 bytes, usart.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART1_IRQHandler &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[8f]"></a>USART_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_usart.o(i.USART_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Uart
</UL>

<P><STRONG><a name="[d8]"></a>USART_GetFlagStatus</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_usart.o(i.USART_GetFlagStatus))
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartSendData
</UL>

<P><STRONG><a name="[d0]"></a>USART_GetITStatus</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, stm32f4xx_usart.o(i.USART_GetITStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_GetITStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[8e]"></a>USART_Init</STRONG> (Thumb, 204 bytes, Stack size 48 bytes, stm32f4xx_usart.o(i.USART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_Uart
</UL>

<P><STRONG><a name="[d1]"></a>USART_ReceiveData</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_usart.o(i.USART_ReceiveData))
<BR><BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[d7]"></a>USART_SendData</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_usart.o(i.USART_SendData))
<BR><BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartSendData
</UL>

<P><STRONG><a name="[9]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>User_main</STRONG> (Thumb, 132 bytes, Stack size 0 bytes, user.o(i.User_main))
<BR><BR>[Stack]<UL><LI>Max Depth = 336 + Unknown Stack Size
<LI>Call Chain = User_main &rArr; MenuHaddler_5 &rArr; Change_Menu &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA4311_Set
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA4311_Init
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_5
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_4
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_3
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_2
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_1
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Init_All
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Disp_Main
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Change_Menu
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(i.main)
</UL>
<P><STRONG><a name="[d3]"></a>W25Q64_Init</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, w25q64.o(i.W25Q64_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = W25Q64_Init &rArr; SPI1_Init &rArr; GPIO_PinAFConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_ReadID
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_SetSpeed
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;font_init
</UL>

<P><STRONG><a name="[ac]"></a>W25Q64_Read</STRONG> (Thumb, 82 bytes, Stack size 24 bytes, w25q64.o(i.W25Q64_Read))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;font_init
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_HzMat_Get
</UL>

<P><STRONG><a name="[d4]"></a>W25Q64_ReadID</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, w25q64.o(i.W25Q64_ReadID))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = W25Q64_ReadID &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI1_ReadWriteByte
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Init
</UL>

<P><STRONG><a name="[78]"></a>_sys_exit</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usart.o(i._sys_exit))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[82]"></a>delay_ms</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, delay.o(i.delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = delay_ms &rArr; delay_us &rArr; OS_SchedUnlock
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OSTimeDly
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;font_init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TFT_LCD_Init
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MyPs2KeyScan
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA4311_Init
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDSDataInit
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;User_main
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_3
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_2
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MenuHaddler_1
</UL>

<P><STRONG><a name="[bf]"></a>delay_us</STRONG> (Thumb, 92 bytes, Stack size 32 bytes, delay.o(i.delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 40 + Unknown Stack Size
<LI>Call Chain = delay_us &rArr; OS_SchedUnlock
</UL>
<BR>[Calls]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_SchedUnlock
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_SchedLock
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PGA4311_SetGain
</UL>

<P><STRONG><a name="[b2]"></a>font_init</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, fontupd.o(i.font_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 76 + Unknown Stack Size
<LI>Call Chain = font_init &rArr; W25Q64_Init &rArr; SPI1_Init &rArr; GPIO_PinAFConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Read
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_LCD_Init
</UL>

<P><STRONG><a name="[73]"></a>main</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 336 + Unknown Stack Size
<LI>Call Chain = main &rArr; OS_Init &rArr; OS_LCD_Init &rArr; OS_String_Show &rArr; OS_Font_Show &rArr; OS_HzMat_Get &rArr; W25Q64_Read &rArr; SPI1_ReadWriteByte
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Task_Create
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Start
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OS_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[81]"></a>sendData</STRONG> (Thumb, 388 bytes, Stack size 40 bytes, drive_communication.o(i.sendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = sendData &rArr; usartSendData
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usartSendData
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_16
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DDSDataInit
</UL>

<P><STRONG><a name="[6d]"></a>_fp_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[106]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[107]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[c0]"></a>PS2_GPIO_Init</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, drive_ps2.o(i.PS2_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = PS2_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_Keyboard_Init
</UL>

<P><STRONG><a name="[c1]"></a>PS2_SCL_Set</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, drive_ps2.o(i.PS2_SCL_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = PS2_SCL_Set &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_ReadKeyCodon
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_SCL_Wait
</UL>

<P><STRONG><a name="[c2]"></a>PS2_SCL_Wait</STRONG> (Thumb, 126 bytes, Stack size 8 bytes, drive_ps2.o(i.PS2_SCL_Wait))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = PS2_SCL_Wait &rArr; PS2_SCL_Set &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadInputDataBit
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_SCL_Set
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PS2_ReadKeyCodon
</UL>

<P><STRONG><a name="[d5]"></a>crc_16</STRONG> (Thumb, 58 bytes, Stack size 12 bytes, drive_communication.o(i.crc_16))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = crc_16
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendData
</UL>

<P><STRONG><a name="[d6]"></a>usartSendData</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, drive_communication.o(i.usartSendData))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usartSendData
</UL>
<BR>[Calls]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sendData
</UL>

<P><STRONG><a name="[ce]"></a>SetSysClock</STRONG> (Thumb, 220 bytes, Stack size 12 bytes, system_stm32f4xx.o(i.SetSysClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SetSysClock
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
