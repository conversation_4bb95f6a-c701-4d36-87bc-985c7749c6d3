#	include "Drive_PGA4311.h"


PGA4311_Struct PGA4311_Data;
/**----------------------------------------------------------------------------
* 函 数 名	     : PGA4311_Init
* 功能说明       : PGA4311通信IO初始化
* 形    参       : 无
* 返 回 值		 ：无
-----------------------------------------------------------------------------*/
void PGA4311_Init()
{
		GPIO_InitTypeDef GPIO_InitStructure;

		RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOG, ENABLE);
		GPIO_InitStructure.GPIO_Speed=GPIO_Speed_50MHz;
		GPIO_InitStructure.GPIO_Mode=GPIO_Mode_OUT;
		GPIO_InitStructure.GPIO_OType=GPIO_OType_PP;
		GPIO_InitStructure.GPIO_PuPd=GPIO_PuPd_NOPULL;
		GPIO_InitStructure.GPIO_Pin=PGA4311_CS | PGA4311_SCLK | PGA4311_SDO;
		GPIO_Init(GPIOG,&GPIO_InitStructure);

		PGA4311_CS_1();
		PGA4311_SCLK_0();
		PGA4311_SDO_1();

		PGA4311_Data.CH4_Gain=PGA4311_Data.CH3_Gain=PGA4311_Data.CH2_Gain=PGA4311_Data.CH1_Gain=1;//一倍增益
		delay_ms(20);
		PGA4311_Set();//设置各通道增益
}

/**----------------------------------------------------------------------------
* 函 数 名	     : PGA4311_SetGain
* 功能说明       : 设置PGA增益（dB）
* 形    参       : data：	4X8位的增益控制字N
* 返 回 值		 	 ：无
* 数据格式			 ： N_CH4 | N_CH3 | N_CH2 | N_CH1 
* Gain (dB) 	=  （N为十进制）
									0 													(N=0)
									31.5 - [0.5 w (255 - N)])dB	(N=1~255) 
									-95.5dB~+31.5dB
N=2*Gain+192
-----------------------------------------------------------------------------*/
void PGA4311_SetGain(u32 data)
{
	u8 i; 
	u32 temp=0x80000000;

	PGA4311_CS_0();
	delay_us(5);
	for(i=0; i<32; i++)
	{
		PGA4311_SCLK_0();
		delay_us(3);
		if(data&temp){
			PGA4311_SDO_1();
		}else{
			PGA4311_SDO_0();
		}
		delay_us(3);
		PGA4311_SCLK_1();
		delay_us(5);
		temp = temp >> 1;
	}
	PGA4311_CS_1();
	PGA4311_SDO_1();
	PGA4311_SCLK_0();
	/* CS触发 */
	PGA4311_CS_0();
	delay_us(5);
	PGA4311_CS_1();
}


/**----------------------------------------------------------------------------
* 函 数 名	     : PGA4311_Set
* 功能说明       : 设置PGA增益（dB）
-----------------------------------------------------------------------------*/
void PGA4311_Set()
{
	//Gain(dB)转换为N
	#define GAIN_TO_N(x) (u8)(2*x+192)
	
	PGA4311_SetGain( GAIN_TO_N(PGA4311_Data.CH4_Gain)<< 24 | GAIN_TO_N(PGA4311_Data.CH3_Gain)<< 16 | GAIN_TO_N(PGA4311_Data.CH2_Gain)<< 8 | GAIN_TO_N(PGA4311_Data.CH1_Gain) );
}
