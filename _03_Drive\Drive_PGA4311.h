#ifndef _PGA4311_H_
#define _PGA4311_H_
/* 头文件包含 ----------------------------------------------------------------*/ 
#include "User_header.h"
/* 通信宏定义 ----------------------------------------------------------------*/
#define		PGA4311_PORT		GPIOG
#define		PGA4311_CS			GPIO_Pin_3
#define		PGA4311_SDO			GPIO_Pin_5
#define		PGA4311_SCLK		GPIO_Pin_7

#define 	PGA4311_CS_0()		GPIO_ResetBits(PGA4311_PORT, PGA4311_CS)
#define 	PGA4311_CS_1()		GPIO_SetBits(PGA4311_PORT, PGA4311_CS)
#define 	PGA4311_SCLK_0()	GPIO_ResetBits(PGA4311_PORT, PGA4311_SCLK)
#define 	PGA4311_SCLK_1()	GPIO_SetBits(PGA4311_PORT, PGA4311_SCLK)
#define 	PGA4311_SDO_0()		GPIO_ResetBits(PGA4311_PORT, PGA4311_SDO)
#define 	PGA4311_SDO_1()		GPIO_SetBits(PGA4311_PORT, PGA4311_SDO)


//PGA4311通道增益结构体
typedef struct
{
	float CH1_Gain;
	float CH2_Gain;
	float CH3_Gain;
	float CH4_Gain;
}PGA4311_Struct;
/* 全局函数编写 --------------------------------------------------------------*/
void PGA4311_Init();
void PGA4311_SetGain(u32 data);
void PGA4311_Set();

extern PGA4311_Struct PGA4311_Data;
#endif 

